const mongoose = require('mongoose');

const videoBlogSchema = new mongoose.Schema({
  title: {
    type: String,
    required: [true, 'Video title is required'],
    trim: true,
    maxlength: [200, 'Title cannot exceed 200 characters']
  },
  videoFileId: {
    type: String,
    required: [true, 'Video file ID is required'],
    trim: true
  },
  videoFilename: {
    type: String,
    required: [true, 'Video filename is required'],
    trim: true
  },
  mimetype: {
    type: String,
    required: [true, 'Video mimetype is required'],
    trim: true
  },
  fileSize: {
    type: Number,
    required: [true, 'File size is required'],
    min: [0, 'File size cannot be negative']
  },
  duration: {
    type: Number, // Duration in seconds
    default: null,
    min: [0, 'Duration cannot be negative']
  },
  thumbnailFileId: {
    type: String,
    default: null,
    trim: true
  },
  thumbnailFilename: {
    type: String,
    default: null,
    trim: true
  },
  isActive: {
    type: Boolean,
    default: true
  },
  views: {
    type: Number,
    default: 0,
    min: [0, 'Views cannot be negative']
  },
  videoUrl: {
    type: String,
    required: [true, 'Video URL is required'],
    trim: true,
    validate: {
      validator: function(v) {
        return /^https?:\/\/.+/.test(v);
      },
      message: 'Video URL must be a valid HTTP/HTTPS URL'
    }
  },
  thumbnailUrl: {
    type: String,
    default: null,
    trim: true,
    validate: {
      validator: function(v) {
        return !v || /^https?:\/\/.+/.test(v);
      },
      message: 'Thumbnail URL must be a valid HTTP/HTTPS URL'
    }
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true
});

// Index for better query performance
videoBlogSchema.index({ createdAt: -1 });
videoBlogSchema.index({ isActive: 1 });
videoBlogSchema.index({ title: 'text' });
videoBlogSchema.index({ videoUrl: 1 });

// Transform the output to include id field
videoBlogSchema.set('toJSON', {
  virtuals: true,
  transform: function(doc, ret) {
    ret.id = ret._id;
    delete ret._id;
    delete ret.__v;
    return ret;
  }
});

videoBlogSchema.set('toObject', {
  virtuals: true,
  transform: function(doc, ret) {
    ret.id = ret._id;
    delete ret._id;
    delete ret.__v;
    return ret;
  }
});

// Pre-save middleware to update the updatedAt field
videoBlogSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

module.exports = mongoose.model('VideoBlog', videoBlogSchema);
